# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import logging

from .base_session_service import BaseSessionService
from .in_memory_session_service import InMemorySessionService
from .session import Session
from .state import State
from .vertex_ai_session_service import VertexAiSessionService

logger = logging.getLogger('google_adk.' + __name__)


__all__ = [
    'BaseSessionService',
    'InMemorySessionService',
    'Session',
    'State',
    'VertexAiSessionService',
]

try:
  from .database_session_service import DatabaseSessionService

  __all__.append('DatabaseSessionService')
except ImportError:
  logger.debug(
      'DatabaseSessionService require sqlalchemy>=2.0, please ensure it is'
      ' installed correctly.'
  )
